# Videos Directory

This directory contains video files used throughout the application.

## Required Videos

### Payment Demo Video
- **File names**: `payment-demo.mp4` and `payment-demo.webm`
- **Location**: `/public/videos/`
- **Purpose**: Demonstrates how to create and share payment links
- **Specifications**:
  - Duration: 30-60 seconds
  - Resolution: 1280x720 (HD) or higher
  - Format: MP4 (H.264) and WebM for browser compatibility
  - Should be optimized for web (small file size)

## How to Add Videos

1. **Create or obtain your video file**
   - Record a screen capture of the payment link creation process
   - Or create an animated demonstration
   - Keep it short and focused (30-60 seconds)

2. **Convert to required formats**
   - Primary: MP4 (H.264 codec)
   - Fallback: WebM (VP9 codec)
   - Use tools like FFmpeg for conversion:
     ```bash
     # Convert to MP4
     ffmpeg -i input.mov -c:v libx264 -c:a aac -crf 23 payment-demo.mp4
     
     # Convert to WebM
     ffmpeg -i input.mov -c:v libvpx-vp9 -c:a libopus payment-demo.webm
     ```

3. **Place files in this directory**
   - `payment-demo.mp4`
   - `payment-demo.webm`

4. **Test the video**
   - Navigate to `/admin/payment-link` in your application
   - The video should auto-play, loop, and be muted
   - If videos are not available, a fallback placeholder will be shown

## Video Guidelines

- **Keep file sizes small** (under 5MB if possible)
- **Ensure videos are web-optimized**
- **Test on different browsers** (Chrome, Firefox, Safari, Edge)
- **Consider mobile viewing** (responsive design)
- **Use appropriate aspect ratios** (16:9 recommended)

## Fallback Behavior

If video files are not found, the application will show a placeholder with:
- Credit card icon
- "Payment Demo Video" text
- "Video not available" message

This ensures the application works even without video files.
