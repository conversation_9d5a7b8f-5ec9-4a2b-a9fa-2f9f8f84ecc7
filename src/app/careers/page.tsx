'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  MapPin,
  Clock,
  Users,
  Briefcase,
  Heart,
  Code,
  Palette,
  TrendingUp,
  Mail,
  ExternalLink,
  Loader2
} from 'lucide-react';
import Link from 'next/link';
import { getCareers, Career } from '@/lib/firebaseServices';
import { toast } from 'sonner';



const benefits = [
  {
    icon: <Heart className="h-6 w-6 text-red-500" />,
    title: 'Health & Wellness',
    description: 'Comprehensive health insurance, dental, vision, and wellness programs'
  },
  {
    icon: <Clock className="h-6 w-6 text-blue-500" />,
    title: 'Flexible Schedule',
    description: 'Work-life balance with flexible hours and remote work options'
  },
  {
    icon: <TrendingUp className="h-6 w-6 text-green-500" />,
    title: 'Growth Opportunities',
    description: 'Professional development, training, and career advancement paths'
  },
  {
    icon: <Users className="h-6 w-6 text-purple-500" />,
    title: 'Great Team',
    description: 'Collaborative environment with talented, passionate colleagues'
  }
];

export default function CareersPage() {
  const [careers, setCareers] = useState<Career[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchCareers();
  }, []);

  const fetchCareers = async () => {
    try {
      setLoading(true);
      const fetchedCareers = await getCareers();
      setCareers(fetchedCareers);
    } catch (error) {
      console.error('Error fetching careers:', error);
      toast.error('Failed to load career opportunities');
    } finally {
      setLoading(false);
    }
  };

  const handleApply = (careerTitle: string) => {
    const subject = `Application for ${careerTitle}`;
    const body = `Dear Hiring Team,\n\nI am interested in applying for the ${careerTitle} position at KaleidoneX.\n\nPlease find my resume attached.\n\nBest regards,`;
    window.location.href = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="container mx-auto px-4 py-20">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Join Our Team
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100">
              Help us build the future of digital templates and creative solutions
            </p>
            <div className="flex flex-wrap justify-center gap-4 text-sm">
              <div className="flex items-center space-x-2">
                <Users className="h-5 w-5" />
                <span>50+ Team Members</span>
              </div>
              <div className="flex items-center space-x-2">
                <MapPin className="h-5 w-5" />
                <span>Remote-First Culture</span>
              </div>
              <div className="flex items-center space-x-2">
                <TrendingUp className="h-5 w-5" />
                <span>Fast Growing</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-16">
        {/* Why Join Us */}
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Why Work With Us?</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-12">
            We're building something amazing, and we want passionate people to join us on this journey.
          </p>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardContent className="pt-6">
                  <div className="flex justify-center mb-4">
                    {benefit.icon}
                  </div>
                  <h3 className="font-semibold text-lg mb-2">{benefit.title}</h3>
                  <p className="text-gray-600 text-sm">{benefit.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Open Positions */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Open Positions</h2>
            <p className="text-xl text-gray-600">
              Find your next opportunity with us
            </p>
          </div>

          {loading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
              <span className="ml-2 text-gray-600">Loading career opportunities...</span>
            </div>
          ) : careers.length > 0 ? (
            <div className="grid gap-6">
              {careers.map((career) => (
                <Card key={career.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                      <div>
                        <CardTitle className="text-xl mb-2">{career.title}</CardTitle>
                        <div className="flex flex-wrap gap-2 mb-2">
                          <Badge variant="outline">{career.department}</Badge>
                          <Badge variant="outline">{career.type}</Badge>
                          <Badge variant="outline">{career.experience}</Badge>
                        </div>
                        <div className="flex items-center text-gray-600 text-sm">
                          <MapPin className="h-4 w-4 mr-1" />
                          {career.location}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-semibold text-green-600 mb-2">
                          {career.salary}
                        </div>
                        <Button onClick={() => handleApply(career.title)}>
                          Apply Now
                          <ExternalLink className="ml-2 h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-700 mb-4">{career.description}</p>
                    <div>
                      <h4 className="font-semibold mb-2">Key Requirements:</h4>
                      <ul className="list-disc list-inside text-sm text-gray-600 space-y-1">
                        {career.requirements.map((req, index) => (
                          <li key={index}>{req}</li>
                        ))}
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Briefcase className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No Open Positions</h3>
              <p className="text-gray-600 mb-4">
                We don't have any open positions at the moment, but we're always looking for talented individuals.
              </p>
              <Button onClick={() => handleApply('General Application')}>
                Send Your Resume
                <Mail className="ml-2 h-4 w-4" />
              </Button>
            </div>
          )}
        </div>

        {/* Contact Section */}
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-0">
          <CardContent className="text-center py-12">
            <Mail className="h-12 w-12 text-blue-600 mx-auto mb-4" />
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Don't See Your Role?
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              We're always looking for talented individuals. Send us your resume and tell us how you'd like to contribute to KaleidoneX.
            </p>
            <Button size="lg" onClick={() => handleApply('General Application')}>
              <Mail className="mr-2 h-5 w-5" />
              Send Your Resume
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
