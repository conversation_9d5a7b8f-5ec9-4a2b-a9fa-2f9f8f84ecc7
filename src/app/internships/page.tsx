'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  MapPin, 
  Clock, 
  Users, 
  GraduationCap, 
  Star, 
  Code, 
  Palette, 
  TrendingUp,
  Mail,
  ExternalLink,
  Calendar,
  Award
} from 'lucide-react';
import Link from 'next/link';

const internshipPrograms = [
  {
    id: 1,
    title: 'Frontend Development Intern',
    department: 'Engineering',
    duration: '3-6 months',
    type: 'Remote/Hybrid',
    level: 'Beginner to Intermediate',
    description: 'Learn modern web development with React, Next.js, and TypeScript while working on real projects.',
    skills: ['React/Next.js', 'JavaScript/TypeScript', 'HTML/CSS', 'Git/GitHub'],
    projects: ['Template marketplace features', 'User dashboard improvements', 'Mobile responsiveness'],
    stipend: '$1,000 - $2,000/month'
  },
  {
    id: 2,
    title: 'UI/UX Design Intern',
    department: 'Design',
    duration: '3-4 months',
    type: 'Remote/Hybrid',
    level: 'Beginner to Intermediate',
    description: 'Create beautiful user interfaces and improve user experience across our platform.',
    skills: ['Figma/Sketch', 'Design Systems', 'User Research', 'Prototyping'],
    projects: ['Template design improvements', 'User onboarding flow', 'Mobile app designs'],
    stipend: '$800 - $1,500/month'
  },
  {
    id: 3,
    title: 'Backend Development Intern',
    department: 'Engineering',
    duration: '4-6 months',
    type: 'Remote/Hybrid',
    level: 'Intermediate',
    description: 'Build scalable backend systems and APIs using modern technologies.',
    skills: ['Node.js/Python', 'Database Design', 'API Development', 'Cloud Platforms'],
    projects: ['API optimization', 'Database improvements', 'Payment integration'],
    stipend: '$1,200 - $2,200/month'
  },
  {
    id: 4,
    title: 'Digital Marketing Intern',
    department: 'Marketing',
    duration: '3-4 months',
    type: 'Remote',
    level: 'Beginner',
    description: 'Learn digital marketing strategies and help grow our online presence.',
    skills: ['Social Media Marketing', 'Content Creation', 'Analytics', 'SEO Basics'],
    projects: ['Social media campaigns', 'Content calendar', 'Market research'],
    stipend: '$600 - $1,200/month'
  }
];

const benefits = [
  {
    icon: <GraduationCap className="h-6 w-6 text-blue-500" />,
    title: 'Learn & Grow',
    description: 'Hands-on experience with cutting-edge technologies and mentorship from senior developers'
  },
  {
    icon: <Users className="h-6 w-6 text-green-500" />,
    title: 'Team Collaboration',
    description: 'Work alongside experienced professionals and contribute to real projects'
  },
  {
    icon: <Award className="h-6 w-6 text-purple-500" />,
    title: 'Certificate & References',
    description: 'Completion certificate and strong references for future opportunities'
  },
  {
    icon: <TrendingUp className="h-6 w-6 text-orange-500" />,
    title: 'Career Path',
    description: 'Potential for full-time offers based on performance and company needs'
  }
];

const requirements = [
  'Currently enrolled in a relevant degree program or recent graduate',
  'Basic knowledge of the field you\'re applying for',
  'Strong communication skills and eagerness to learn',
  'Ability to commit to the full internship duration',
  'Portfolio or projects (for technical roles)',
  'Passion for technology and digital innovation'
];

export default function InternshipsPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-green-600 to-blue-600 text-white">
        <div className="container mx-auto px-4 py-20">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Internship Programs
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-green-100">
              Launch your career with hands-on experience at KaleidoneX
            </p>
            <div className="flex flex-wrap justify-center gap-4 text-sm">
              <div className="flex items-center space-x-2">
                <Calendar className="h-5 w-5" />
                <span>3-6 Month Programs</span>
              </div>
              <div className="flex items-center space-x-2">
                <MapPin className="h-5 w-5" />
                <span>Remote & Hybrid Options</span>
              </div>
              <div className="flex items-center space-x-2">
                <Award className="h-5 w-5" />
                <span>Paid Internships</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-16">
        {/* Why Intern With Us */}
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Why Intern With Us?</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-12">
            Get real-world experience, learn from industry experts, and kickstart your career in tech.
          </p>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardContent className="pt-6">
                  <div className="flex justify-center mb-4">
                    {benefit.icon}
                  </div>
                  <h3 className="font-semibold text-lg mb-2">{benefit.title}</h3>
                  <p className="text-gray-600 text-sm">{benefit.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Available Programs */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Available Programs</h2>
            <p className="text-xl text-gray-600">
              Choose the program that matches your interests and skills
            </p>
          </div>

          <div className="grid gap-6">
            {internshipPrograms.map((program) => (
              <Card key={program.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                    <div>
                      <CardTitle className="text-xl mb-2">{program.title}</CardTitle>
                      <div className="flex flex-wrap gap-2 mb-2">
                        <Badge variant="outline">{program.department}</Badge>
                        <Badge variant="outline">{program.type}</Badge>
                        <Badge variant="outline">{program.duration}</Badge>
                        <Badge variant="outline">{program.level}</Badge>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-semibold text-green-600 mb-2">
                        {program.stipend}
                      </div>
                      <Button>
                        Apply Now
                        <ExternalLink className="ml-2 h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700 mb-4">{program.description}</p>
                  
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-semibold mb-2">Skills You'll Learn:</h4>
                      <ul className="list-disc list-inside text-sm text-gray-600 space-y-1">
                        {program.skills.map((skill, index) => (
                          <li key={index}>{skill}</li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2">Projects You'll Work On:</h4>
                      <ul className="list-disc list-inside text-sm text-gray-600 space-y-1">
                        {program.projects.map((project, index) => (
                          <li key={index}>{project}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Requirements */}
        <div className="mb-16">
          <Card>
            <CardHeader>
              <CardTitle className="text-2xl">General Requirements</CardTitle>
              <CardDescription>
                What we look for in our interns
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3">
                {requirements.map((requirement, index) => (
                  <li key={index} className="flex items-start space-x-3">
                    <Star className="h-5 w-5 text-yellow-500 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700">{requirement}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>

        {/* Application Process */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">Application Process</h2>
          <div className="grid md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-blue-600 font-bold">1</span>
              </div>
              <h3 className="font-semibold mb-2">Apply Online</h3>
              <p className="text-sm text-gray-600">Submit your application with resume and portfolio</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-green-600 font-bold">2</span>
              </div>
              <h3 className="font-semibold mb-2">Initial Review</h3>
              <p className="text-sm text-gray-600">We review your application and portfolio</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-purple-600 font-bold">3</span>
              </div>
              <h3 className="font-semibold mb-2">Interview</h3>
              <p className="text-sm text-gray-600">Virtual interview with our team</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-orange-600 font-bold">4</span>
              </div>
              <h3 className="font-semibold mb-2">Start Internship</h3>
              <p className="text-sm text-gray-600">Begin your journey with us!</p>
            </div>
          </div>
        </div>

        {/* Contact Section */}
        <Card className="bg-gradient-to-r from-green-50 to-blue-50 border-0">
          <CardContent className="text-center py-12">
            <Mail className="h-12 w-12 text-green-600 mx-auto mb-4" />
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Ready to Start Your Journey?
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Have questions about our internship programs? We'd love to hear from you and help you find the perfect opportunity.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" asChild>
                <Link href="mailto:<EMAIL>">
                  <Mail className="mr-2 h-5 w-5" />
                  Apply for Internship
                </Link>
              </Button>
              <Button size="lg" variant="outline" asChild>
                <Link href="mailto:<EMAIL>">
                  Ask Questions
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
