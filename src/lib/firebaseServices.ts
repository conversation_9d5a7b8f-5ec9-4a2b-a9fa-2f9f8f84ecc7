import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  query,
  orderBy,
  onSnapshot,
  where
} from 'firebase/firestore';
import { db } from './firebase';
import { CustomRequest, User, ContactMessage, Template } from '@/types';

// Custom Requests Services
export const createCustomRequest = async (requestData: Omit<CustomRequest, 'id' | 'createdAt' | 'updatedAt'>) => {
  try {
    // Filter out undefined values to prevent Firebase errors
    const cleanedData = Object.fromEntries(
      Object.entries(requestData).filter(([_, value]) => value !== undefined)
    );

    const docRef = await addDoc(collection(db, 'customRequests'), {
      ...cleanedData,
      createdAt: new Date(),
      updatedAt: new Date()
    });
    return docRef.id;
  } catch (error) {
    console.error('Error creating custom request:', error);
    throw error;
  }
};

export const getCustomRequests = async () => {
  try {
    const q = query(collection(db, 'customRequests'), orderBy('createdAt', 'desc'));
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as CustomRequest[];
  } catch (error) {
    console.error('Error fetching custom requests:', error);
    throw error;
  }
};

export const updateCustomRequestStatus = async (requestId: string, status: CustomRequest['status'], adminNotes?: string) => {
  try {
    const updateData: any = {
      status,
      updatedAt: new Date()
    };

    if (adminNotes) {
      updateData.adminNotes = adminNotes;
    }

    // Set default payment status when completing a request
    if (status === 'completed') {
      updateData.paymentStatus = 'pending';
    }

    await updateDoc(doc(db, 'customRequests', requestId), updateData);
  } catch (error) {
    console.error('Error updating custom request:', error);
    throw error;
  }
};

export const updateCustomRequestPaymentStatus = async (requestId: string, paymentStatus: CustomRequest['paymentStatus']) => {
  try {
    const updateData: any = {
      paymentStatus,
      updatedAt: new Date()
    };

    await updateDoc(doc(db, 'customRequests', requestId), updateData);
  } catch (error) {
    console.error('Error updating custom request payment status:', error);
    throw error;
  }
};

// User Management Services
export const getAllUsers = async () => {
  try {
    const q = query(collection(db, 'users'), orderBy('createdAt', 'desc'));
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as User[];
  } catch (error) {
    console.error('Error fetching users:', error);
    throw error;
  }
};

export const getUserById = async (userId: string) => {
  try {
    const docRef = doc(db, 'users', userId);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return { id: docSnap.id, ...docSnap.data() } as User;
    } else {
      throw new Error('User not found');
    }
  } catch (error) {
    console.error('Error fetching user:', error);
    throw error;
  }
};

// Dashboard Statistics Services
export const getDashboardStats = async () => {
  try {
    const [usersSnapshot, templatesSnapshot, requestsSnapshot] = await Promise.all([
      getDocs(collection(db, 'users')),
      getDocs(collection(db, 'templates')),
      getDocs(collection(db, 'customRequests'))
    ]);

    const totalUsers = usersSnapshot.size;
    const totalTemplates = templatesSnapshot.size;
    const totalRequests = requestsSnapshot.size;
    
    // Calculate pending requests
    const pendingRequests = requestsSnapshot.docs.filter(
      doc => doc.data().status === 'pending'
    ).length;

    return {
      totalUsers,
      totalTemplates,
      totalRequests,
      pendingRequests,
      totalSales: 0, // Placeholder for sales data
      customizations: 0 // Placeholder for customizations data
    };
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    throw error;
  }
};

// Real-time listeners
export const subscribeToCustomRequests = (callback: (requests: CustomRequest[]) => void) => {
  const q = query(collection(db, 'customRequests'), orderBy('createdAt', 'desc'));
  
  return onSnapshot(q, (querySnapshot) => {
    const requests = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as CustomRequest[];
    callback(requests);
  });
};

export const subscribeToUsers = (callback: (users: User[]) => void) => {
  const q = query(collection(db, 'users'), orderBy('createdAt', 'desc'));

  return onSnapshot(q, (querySnapshot) => {
    const users = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as User[];
    callback(users);
  });
};

// Contact Messages Services
export const createContactMessage = async (messageData: Omit<ContactMessage, 'id' | 'createdAt' | 'updatedAt'>) => {
  try {
    const docRef = await addDoc(collection(db, 'contactMessages'), {
      ...messageData,
      createdAt: new Date(),
      updatedAt: new Date()
    });
    return docRef.id;
  } catch (error) {
    console.error('Error creating contact message:', error);
    throw error;
  }
};

export const getContactMessages = async () => {
  try {
    const q = query(collection(db, 'contactMessages'), orderBy('createdAt', 'desc'));
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as ContactMessage[];
  } catch (error) {
    console.error('Error fetching contact messages:', error);
    throw error;
  }
};

export const updateContactMessageStatus = async (messageId: string, status: ContactMessage['status'], adminNotes?: string) => {
  try {
    const updateData: any = {
      status,
      updatedAt: new Date()
    };

    if (adminNotes) {
      updateData.adminNotes = adminNotes;
    }

    await updateDoc(doc(db, 'contactMessages', messageId), updateData);
  } catch (error) {
    console.error('Error updating contact message:', error);
    throw error;
  }
};

export const subscribeToContactMessages = (callback: (messages: ContactMessage[]) => void) => {
  const q = query(collection(db, 'contactMessages'), orderBy('createdAt', 'desc'));

  return onSnapshot(q, (querySnapshot) => {
    const messages = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as ContactMessage[];
    callback(messages);
  });
};

// Template Services
export const addTemplate = async (templateData: Omit<Template, 'id' | 'createdAt' | 'updatedAt'>) => {
  try {
    // Filter out undefined values to prevent Firebase errors
    const cleanedData = Object.fromEntries(
      Object.entries(templateData).filter(([_, value]) => value !== undefined)
    );

    const docRef = await addDoc(collection(db, 'templates'), {
      ...cleanedData,
      createdAt: new Date(),
      updatedAt: new Date()
    });
    return docRef.id;
  } catch (error) {
    console.error('Error adding template:', error);
    throw error;
  }
};

export const getTemplates = async () => {
  try {
    const q = query(collection(db, 'templates'), orderBy('createdAt', 'desc'));
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as Template[];
  } catch (error) {
    console.error('Error fetching templates:', error);
    throw error;
  }
};

export const updateTemplate = async (templateId: string, templateData: Partial<Template>) => {
  try {
    // Filter out undefined values
    const cleanedData = Object.fromEntries(
      Object.entries(templateData).filter(([_, value]) => value !== undefined)
    );

    await updateDoc(doc(db, 'templates', templateId), {
      ...cleanedData,
      updatedAt: new Date()
    });
  } catch (error) {
    console.error('Error updating template:', error);
    throw error;
  }
};

export const deleteTemplate = async (templateId: string) => {
  try {
    await updateDoc(doc(db, 'templates', templateId), {
      deleted: true,
      updatedAt: new Date()
    });
  } catch (error) {
    console.error('Error deleting template:', error);
    throw error;
  }
};

// Favorites Services
export const toggleFavorite = async (userId: string, templateId: string) => {
  try {
    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);

    if (userDoc.exists()) {
      const userData = userDoc.data();
      const currentFavorites = userData.favoriteTemplates || [];

      let updatedFavorites;
      if (currentFavorites.includes(templateId)) {
        // Remove from favorites
        updatedFavorites = currentFavorites.filter((id: string) => id !== templateId);
      } else {
        // Add to favorites
        updatedFavorites = [...currentFavorites, templateId];
      }

      await updateDoc(userRef, {
        favoriteTemplates: updatedFavorites,
        updatedAt: new Date()
      });

      return !currentFavorites.includes(templateId); // Return true if added, false if removed
    }
  } catch (error) {
    console.error('Error toggling favorite:', error);
    throw error;
  }
};

export const removeFavorite = async (userId: string, templateId: string) => {
  try {
    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);

    if (userDoc.exists()) {
      const userData = userDoc.data();
      const currentFavorites = userData.favoriteTemplates || [];
      const updatedFavorites = currentFavorites.filter((id: string) => id !== templateId);

      await updateDoc(userRef, {
        favoriteTemplates: updatedFavorites,
        updatedAt: new Date()
      });
    }
  } catch (error) {
    console.error('Error removing favorite:', error);
    throw error;
  }
};

// Chatbot Services
export interface ChatbotQA {
  id: string;
  question: string;
  answer: string;
  category: string;
  order: number;
  active: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export const getChatbotQAs = async (): Promise<ChatbotQA[]> => {
  try {
    const qasQuery = query(
      collection(db, 'chatbot_qas'),
      where('active', '==', true),
      orderBy('order', 'asc')
    );
    const querySnapshot = await getDocs(qasQuery);

    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
      createdAt: doc.data().createdAt?.toDate() || new Date(),
      updatedAt: doc.data().updatedAt?.toDate() || new Date(),
    })) as ChatbotQA[];
  } catch (error) {
    console.error('Error fetching chatbot QAs:', error);
    throw error;
  }
};

export const createChatbotQA = async (qaData: Omit<ChatbotQA, 'id' | 'createdAt' | 'updatedAt'>) => {
  try {
    // Validate required fields
    if (!qaData.question || !qaData.answer || !qaData.category) {
      throw new Error('Question, answer, and category are required fields');
    }

    // Filter out undefined values to prevent Firebase errors
    const cleanedData = Object.fromEntries(
      Object.entries(qaData).filter(([_, value]) => value !== undefined)
    );

    const docRef = await addDoc(collection(db, 'chatbot_qas'), {
      ...cleanedData,
      createdAt: new Date(),
      updatedAt: new Date()
    });
    return docRef.id;
  } catch (error) {
    console.error('Error creating chatbot QA:', error);
    throw error;
  }
};

export const updateChatbotQA = async (id: string, qaData: Partial<ChatbotQA>) => {
  try {
    // Filter out undefined values to prevent Firebase errors
    const cleanedData = Object.fromEntries(
      Object.entries(qaData).filter(([_, value]) => value !== undefined)
    );

    await updateDoc(doc(db, 'chatbot_qas', id), {
      ...cleanedData,
      updatedAt: new Date()
    });
  } catch (error) {
    console.error('Error updating chatbot QA:', error);
    throw error;
  }
};

export const deleteChatbotQA = async (id: string) => {
  try {
    await updateDoc(doc(db, 'chatbot_qas', id), {
      active: false,
      updatedAt: new Date()
    });
  } catch (error) {
    console.error('Error deleting chatbot QA:', error);
    throw error;
  }
};
